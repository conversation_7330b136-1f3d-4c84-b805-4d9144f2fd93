local function getAllFruits()
    local fruitSet = {}
    local fruitList = {}

    local plantTraitsData = filtergc("table", {
        Keys = {"Fruit", "Berry", "Sweet"}
    }, true)

    local growableData = filtergc("table", {
        KeyValuePairs = {PlantName = "Apple"}
    }, true)

    if not growableData then
        growableData = filtergc("table", {
            Keys = {"Apple", "Banana", "Coconut"}
        }, true)
    end

    if plantTraitsData and plantTraitsData.Fruit then
        for fruitName in pairs(plantTraitsData.Fruit) do
            if not fruitSet[fruitName] then
                fruitSet[fruitName] = true
                table.insert(fruitList, fruitName)
            end
        end
    end

    if growableData then
        for _, plantData in pairs(growableData) do
            if type(plantData) == "table" and plantData.PlantName then
                local plantName = plantData.PlantName

                if plantData.FruitData or
                   plantName:find("Fruit") or
                   plantName:find("Berry") or
                   plantName:find("Melon") or
                   plantName == "Tomato" or
                   plantName == "Corn" then

                    if not fruitSet[plantName] then
                        fruitSet[plantName] = true
                        table.insert(fruitList, plantName)
                    end
                end
            end
        end
    end

    if #fruitList == 0 then
        local replicatedStorage = game:GetService("ReplicatedStorage")
        local success1, plantTraits = pcall(function()
            return require(replicatedStorage.Modules.PlantTraitsData)
        end)
        local success2, growable = pcall(function()
            return require(replicatedStorage.Data.GrowableData)
        end)

        if success1 and plantTraits.Fruit then
            for fruitName in pairs(plantTraits.Fruit) do
                if not fruitSet[fruitName] then
                    fruitSet[fruitName] = true
                    table.insert(fruitList, fruitName)
                end
            end
        end

        if success2 then
            for _, plantData in pairs(growable) do
                if type(plantData) == "table" and plantData.PlantName then
                    local plantName = plantData.PlantName

                    if plantData.FruitData or
                       plantName:find("Fruit") or
                       plantName:find("Berry") or
                       plantName:find("Melon") or
                       plantName == "Tomato" or
                       plantName == "Corn" then

                        if not fruitSet[plantName] then
                            fruitSet[plantName] = true
                            table.insert(fruitList, plantName)
                        end
                    end
                end
            end
        end
    end

    table.sort(fruitList)
    return fruitList
end

local fruitList = getAllFruits()

print("All fruits in the game:")
for i, fruitName in ipairs(fruitList) do
    print(i .. ". " .. fruitName)
end

print("\nTotal fruits found: " .. #fruitList)

return fruitList